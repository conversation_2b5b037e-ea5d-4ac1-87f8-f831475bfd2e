// Configuración de Supabase
const SUPABASE_URL = 'https://zeieudvbhlrlnfkwejoh.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InplaWV1ZHZiaGxybG5ma3dlam9oIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzM5NTU5NzQsImV4cCI6MjA0OTUzMTk3NH0.Ej4Ej4Ej4Ej4Ej4Ej4Ej4Ej4Ej4Ej4Ej4Ej4Ej4Ej4'; // Placeholder - usar clave real

// Datos de ejemplo para demostración (en producción se conectaría a Supabase)
const sampleProcedures = [
    {
        type: 'TRAMITE',
        name: 'Licencia de construcción',
        description: 'Autorización para construcción de obras civiles',
        dependency_name: 'Secretaría de Planeación',
        cost: 419.00,
        response_time: '45 días hábiles'
    },
    {
        type: 'TRAMITE',
        name: 'Certificado de libertad y tradición',
        description: 'Certificado de propiedad inmobiliaria',
        dependency_name: 'Secretaría General',
        cost: 0,
        response_time: '1 día hábil'
    },
    {
        type: 'OPA',
        name: 'Información sobre impuesto predial',
        description: 'Consulta sobre liquidación de impuesto predial',
        dependency_name: 'Secretaría de Hacienda',
        cost: 0,
        response_time: '1 hora'
    },
    {
        type: 'TRAMITE',
        name: 'Licencia de funcionamiento',
        description: 'Autorización para funcionamiento de establecimientos comerciales',
        dependency_name: 'Secretaría de Gobierno',
        cost: 50.00,
        response_time: '15 días hábiles'
    },
    {
        type: 'OPA',
        name: 'Información sobre programas educativos',
        description: 'Consulta sobre programas de educación municipal',
        dependency_name: 'Secretaría de Educación',
        cost: 0,
        response_time: '2 horas'
    },
    {
        type: 'TRAMITE',
        name: 'Permiso de movilización de ganado',
        description: 'Autorización para transporte de ganado',
        dependency_name: 'Secretaría de Desarrollo Económico',
        cost: 25.00,
        response_time: '3 días hábiles'
    },
    {
        type: 'OPA',
        name: 'Información sobre servicios de salud',
        description: 'Consulta sobre centros de salud municipales',
        dependency_name: 'Secretaría de Salud',
        cost: 0,
        response_time: '1 hora'
    },
    {
        type: 'TRAMITE',
        name: 'Licencia ambiental',
        description: 'Autorización para actividades que afecten el medio ambiente',
        dependency_name: 'Secretaría de Medio Ambiente',
        cost: 100.00,
        response_time: '30 días hábiles'
    }
];

// Variables globales
let currentResults = [];
let isSearching = false;

// Función principal de búsqueda
async function searchProcedures() {
    if (isSearching) return;
    
    const searchTerm = document.getElementById('searchInput').value.trim();
    const dependency = document.getElementById('dependencyFilter').value;
    const type = document.getElementById('typeFilter').value;
    
    // Mostrar indicador de carga
    showLoading(true);
    hideNoResults();
    clearResults();
    
    isSearching = true;
    
    try {
        // Simular delay de búsqueda
        await new Promise(resolve => setTimeout(resolve, 800));
        
        // Filtrar resultados (en producción sería una consulta a Supabase)
        let results = [...sampleProcedures];
        
        // Filtrar por término de búsqueda
        if (searchTerm) {
            results = results.filter(proc => 
                proc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                proc.description.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }
        
        // Filtrar por dependencia
        if (dependency) {
            results = results.filter(proc => proc.dependency_name === dependency);
        }
        
        // Filtrar por tipo
        if (type) {
            results = results.filter(proc => proc.type === type);
        }
        
        currentResults = results;
        displayResults(results);
        
    } catch (error) {
        console.error('Error en la búsqueda:', error);
        showError('Error al realizar la búsqueda. Por favor, intenta nuevamente.');
    } finally {
        showLoading(false);
        isSearching = false;
    }
}

// Mostrar resultados
function displayResults(results) {
    const container = document.getElementById('resultsContainer');
    
    if (results.length === 0) {
        showNoResults();
        return;
    }
    
    const resultsHTML = results.map(procedure => `
        <div class="procedure-card">
            <div class="d-flex justify-content-between align-items-start mb-2">
                <div>
                    <span class="procedure-type ${procedure.type === 'TRAMITE' ? 'type-tramite' : 'type-opa'}">
                        ${procedure.type}
                    </span>
                    <h5 class="mt-2 mb-1">${procedure.name}</h5>
                </div>
                <div class="text-end">
                    <span class="cost-badge ${procedure.cost === 0 ? 'cost-free' : ''}">
                        ${procedure.cost === 0 ? 'Gratuito' : `$${procedure.cost.toLocaleString()}`}
                    </span>
                </div>
            </div>
            
            <p class="text-muted mb-2">${procedure.description}</p>
            
            <div class="row">
                <div class="col-md-6">
                    <small class="text-muted">
                        <i class="fas fa-building me-1"></i>
                        ${procedure.dependency_name}
                    </small>
                </div>
                <div class="col-md-6 text-end">
                    <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>
                        ${procedure.response_time}
                    </small>
                </div>
            </div>
            
            <div class="mt-3">
                <button class="btn btn-outline-primary btn-sm me-2" onclick="showProcedureDetails('${procedure.name}')">
                    <i class="fas fa-info-circle me-1"></i>Ver Detalles
                </button>
                <button class="btn btn-primary btn-sm" onclick="startProcedure('${procedure.name}')">
                    <i class="fas fa-play me-1"></i>Iniciar Trámite
                </button>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = `
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h4><i class="fas fa-list me-2"></i>Resultados de Búsqueda</h4>
            <span class="badge bg-primary fs-6">${results.length} procedimiento${results.length !== 1 ? 's' : ''} encontrado${results.length !== 1 ? 's' : ''}</span>
        </div>
        ${resultsHTML}
    `;
}

// Limpiar búsqueda
function clearSearch() {
    document.getElementById('searchInput').value = '';
    document.getElementById('dependencyFilter').value = '';
    document.getElementById('typeFilter').value = '';
    clearResults();
    hideNoResults();
}

// Funciones auxiliares
function showLoading(show) {
    document.getElementById('loadingIndicator').style.display = show ? 'block' : 'none';
}

function showNoResults() {
    document.getElementById('noResults').style.display = 'block';
}

function hideNoResults() {
    document.getElementById('noResults').style.display = 'none';
}

function clearResults() {
    document.getElementById('resultsContainer').innerHTML = '';
}

function showError(message) {
    const container = document.getElementById('resultsContainer');
    container.innerHTML = `
        <div class="alert alert-danger" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            ${message}
        </div>
    `;
}

// Funciones de interacción
function showProcedureDetails(procedureName) {
    const procedure = currentResults.find(p => p.name === procedureName);
    if (!procedure) return;
    
    alert(`Detalles del procedimiento:\n\nNombre: ${procedure.name}\nDescripción: ${procedure.description}\nDependencia: ${procedure.dependency_name}\nCosto: ${procedure.cost === 0 ? 'Gratuito' : '$' + procedure.cost.toLocaleString()}\nTiempo de respuesta: ${procedure.response_time}`);
}

function startProcedure(procedureName) {
    alert(`Funcionalidad en desarrollo.\n\nPróximamente podrás iniciar el trámite "${procedureName}" directamente desde esta plataforma.\n\nPor ahora, puedes acercarte a la dependencia correspondiente con la información mostrada.`);
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Búsqueda al presionar Enter
    document.getElementById('searchInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchProcedures();
        }
    });
    
    // Búsqueda automática al cambiar filtros
    document.getElementById('dependencyFilter').addEventListener('change', function() {
        if (this.value || document.getElementById('searchInput').value.trim()) {
            searchProcedures();
        }
    });
    
    document.getElementById('typeFilter').addEventListener('change', function() {
        if (this.value || document.getElementById('searchInput').value.trim()) {
            searchProcedures();
        }
    });
});

// Función para conectar con Supabase (para implementación futura)
async function searchSupabase(searchTerm, dependency, type) {
    // Esta función se implementaría para conectar con la base de datos real
    const { createClient } = supabase;
    const supabaseClient = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
    
    let query = supabaseClient
        .from('all_procedures')
        .select('*');
    
    if (searchTerm) {
        query = query.or(`name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`);
    }
    
    if (dependency) {
        query = query.eq('dependency_name', dependency);
    }
    
    if (type) {
        query = query.eq('type', type);
    }
    
    const { data, error } = await query.limit(50);
    
    if (error) {
        throw error;
    }
    
    return data;
}
