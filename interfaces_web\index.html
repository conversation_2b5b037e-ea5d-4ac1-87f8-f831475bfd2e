<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistema de Trámites Municipales - Chía</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2E7D32;
            --secondary-color: #4CAF50;
            --accent-color: #81C784;
            --text-dark: #1B5E20;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #E8F5E8 0%, #F1F8E9 100%);
            min-height: 100vh;
        }
        
        .header-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 2rem 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .search-container {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            margin-top: -50px;
            position: relative;
            z-index: 10;
        }
        
        .procedure-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border-left: 4px solid var(--secondary-color);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .procedure-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .procedure-type {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .type-tramite {
            background: #E3F2FD;
            color: #1976D2;
        }
        
        .type-opa {
            background: #F3E5F5;
            color: #7B1FA2;
        }
        
        .cost-badge {
            background: var(--accent-color);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-weight: 600;
        }
        
        .cost-free {
            background: #4CAF50;
        }
        
        .stats-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            height: 100%;
        }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }
        
        .btn-search {
            background: var(--secondary-color);
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-search:hover {
            background: var(--primary-color);
            transform: translateY(-1px);
        }
        
        .dependency-filter {
            background: #F8F9FA;
            border: 1px solid #E9ECEF;
            border-radius: 8px;
            padding: 0.5rem;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 2rem;
        }
        
        .no-results {
            text-align: center;
            padding: 3rem;
            color: #6C757D;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="fas fa-building me-3"></i>
                        Sistema de Trámites Municipales
                    </h1>
                    <h4 class="mb-0 opacity-90">Municipio de Chía, Cundinamarca</h4>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex justify-content-end align-items-center">
                        <div class="me-3">
                            <div class="stats-number text-white">829</div>
                            <small>Procedimientos Disponibles</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Section -->
    <div class="container">
        <div class="search-container">
            <div class="row">
                <div class="col-12">
                    <h3 class="text-center mb-4 text-dark">
                        <i class="fas fa-search me-2"></i>
                        Buscar Trámites y Procedimientos
                    </h3>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label class="form-label fw-bold">Buscar por palabra clave</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" class="form-control" id="searchInput" 
                               placeholder="Ej: licencia, impuesto, certificado...">
                    </div>
                </div>
                
                <div class="col-md-4 mb-3">
                    <label class="form-label fw-bold">Filtrar por dependencia</label>
                    <select class="form-select dependency-filter" id="dependencyFilter">
                        <option value="">Todas las dependencias</option>
                        <option value="Secretaría General">Secretaría General</option>
                        <option value="Secretaría de Hacienda">Secretaría de Hacienda</option>
                        <option value="Secretaría de Gobierno">Secretaría de Gobierno</option>
                        <option value="Secretaría de Educación">Secretaría de Educación</option>
                        <option value="Secretaría de Desarrollo Social">Secretaría de Desarrollo Social</option>
                        <option value="Secretaría de Planeación">Secretaría de Planeación</option>
                        <option value="Secretaría de Movilidad">Secretaría de Movilidad</option>
                        <option value="Secretaría de Medio Ambiente">Secretaría de Medio Ambiente</option>
                        <option value="Secretaría de Obras Públicas">Secretaría de Obras Públicas</option>
                        <option value="Secretaría de Salud">Secretaría de Salud</option>
                        <option value="Secretaría de Desarrollo Económico">Secretaría de Desarrollo Económico</option>
                        <option value="Despacho del Alcalde">Despacho del Alcalde</option>
                    </select>
                </div>
                
                <div class="col-md-2 mb-3">
                    <label class="form-label fw-bold">Tipo</label>
                    <select class="form-select" id="typeFilter">
                        <option value="">Todos</option>
                        <option value="TRAMITE">Trámites</option>
                        <option value="OPA">OPAs</option>
                    </select>
                </div>
            </div>
            
            <div class="row">
                <div class="col-12 text-center">
                    <button class="btn btn-search btn-primary" onclick="searchProcedures()">
                        <i class="fas fa-search me-2"></i>Buscar Procedimientos
                    </button>
                    <button class="btn btn-outline-secondary ms-2" onclick="clearSearch()">
                        <i class="fas fa-times me-2"></i>Limpiar
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Section -->
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-3 mb-4">
                <div class="stats-card">
                    <div class="stats-number">108</div>
                    <h6 class="text-muted">Trámites</h6>
                    <small class="text-muted">Procedimientos formales</small>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="stats-card">
                    <div class="stats-number">721</div>
                    <h6 class="text-muted">OPAs</h6>
                    <small class="text-muted">Otros procedimientos</small>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="stats-card">
                    <div class="stats-number">12</div>
                    <h6 class="text-muted">Dependencias</h6>
                    <small class="text-muted">Secretarías activas</small>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="stats-card">
                    <div class="stats-number">85</div>
                    <h6 class="text-muted">Gratuitos</h6>
                    <small class="text-muted">Trámites sin costo</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Section -->
    <div class="container mt-4">
        <div class="loading" id="loadingIndicator">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Cargando...</span>
            </div>
            <p class="mt-2">Buscando procedimientos...</p>
        </div>
        
        <div id="resultsContainer">
            <!-- Results will be populated here -->
        </div>
        
        <div class="no-results" id="noResults" style="display: none;">
            <i class="fas fa-search fa-3x text-muted mb-3"></i>
            <h4>No se encontraron resultados</h4>
            <p>Intenta con otros términos de búsqueda o selecciona una dependencia diferente.</p>
        </div>
    </div>

    <!-- Footer -->
    <footer class="mt-5 py-4" style="background: var(--primary-color); color: white;">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h6><i class="fas fa-building me-2"></i>Alcaldía Municipal de Chía</h6>
                    <p class="mb-0">Sistema de Trámites y Procedimientos Administrativos</p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="mb-0">
                        <i class="fas fa-phone me-2"></i>Línea de Atención: (*************
                    </p>
                    <p class="mb-0">
                        <i class="fas fa-envelope me-2"></i><EMAIL>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="search.js"></script>
</body>
</html>
